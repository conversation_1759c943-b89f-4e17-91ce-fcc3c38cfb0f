package com.fx.tdengine.controller;


import com.fx.common.core.domain.R;
import com.fx.common.core.web.controller.BaseController;
import com.fx.tdengine.api.domain.SelectDto;
import com.fx.tdengine.api.domain.SysInfo;
import com.fx.tdengine.api.domain.SysInfoVo;
import com.fx.tdengine.service.SystemInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.util.Util;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Properties;

@RestController
@RequestMapping("/systemInfo")
public class SystemInfoController extends BaseController {
    private static final int OSHI_WAIT_SECOND = 1000;
    @Autowired
    private SystemInfoService systemInfoService;
    /**
     *
     */
    private SysInfo cpu = new SysInfo();

    /**
     * 首页系统信息保存
     */
    @PostMapping(value = "/saveSysInfo")
    public R saveSysInfo() throws UnknownHostException {

        SystemInfo si = new SystemInfo();
        HardwareAbstractionLayer hal = si.getHardware();
        setCpuInfo(hal.getProcessor());
        setJvmInfo();
        systemInfoService.add(cpu);
        return R.ok();
    }

    /**
     * 设置CPU信息
     */
    private void setCpuInfo(CentralProcessor processor) {
        // CPU信息
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        Util.sleep(OSHI_WAIT_SECOND);
        long[] ticks = processor.getSystemCpuLoadTicks();
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()] - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()] - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softirq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()] - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()] - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];
        long cSys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()] - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long user = ticks[CentralProcessor.TickType.USER.getIndex()] - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long iowait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()] - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()] - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long totalCpu = user + nice + cSys + idle + iowait + irq + softirq + steal;
        cpu.setCpuNum(processor.getLogicalProcessorCount());
        cpu.setCpuTotal(totalCpu);
        cpu.setCpuSys(cSys);
        cpu.setCpuUsed(user);
        cpu.setCpuWait(iowait);
        cpu.setCpuFree(idle);
    }

    /**
     * 设置Java虚拟机
     */
    private void setJvmInfo() throws UnknownHostException {
        Properties props = System.getProperties();
        cpu.setJvmTotal(Runtime.getRuntime().totalMemory());
        cpu.setJvmMax(Runtime.getRuntime().maxMemory());
        cpu.setJvmFree(Runtime.getRuntime().freeMemory());
        cpu.setJvmUsage(0);

    }

    /**
     * 首页系统信息统计
     */
    @PostMapping(value = "/findSysInfo")
    public R<?> findSysInfo(@RequestBody SelectDto selectDto) {
        List<SysInfoVo> sysInfo = systemInfoService.findSysInfo(selectDto);
        for (SysInfoVo info : sysInfo) {
            String time = info.getTime();
            info.setTime(time.substring(11, 16));
        }
        return R.ok(sysInfo);
    }

}
