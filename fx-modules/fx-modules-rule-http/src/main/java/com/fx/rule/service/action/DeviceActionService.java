package com.fx.rule.service.action;

import com.fx.common.core.domain.ThingModelMessage;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DeviceActionService {

//    public String invoke(Service service) {
//        String[] pkDn = service.get().split("/");
//        ThingService<Map<String, Object>> thingService = new ThingService<>();
//        thingService.setMid(UniqueIdUtil.newRequestId());
//        thingService.setProductKey(pkDn[0]);
//        thingService.setDeviceName(pkDn[1]);
//        thingService.setType(service.getType());
//        thingService.setIdentifier(service.getIdentifier());
//        thingService.setParams(service.parseInputData());
//        deviceComponentManager.send(thingService);
//        return thingService.getMid();
//    }

    private String identifier;

    private List<Parameter> inputData;

    public String getType() {
        //identifier为set固定为属性设置，其它为服务调用
        if (ThingModelMessage.ID_PROPERTY_SET.equals(identifier) ||
                ThingModelMessage.ID_PROPERTY_GET.equals(identifier)) {
            return ThingModelMessage.TYPE_PROPERTY;
        }
        return ThingModelMessage.TYPE_SERVICE;
    }

    public Map<String, Object> parseInputData() {
        Map<String, Object> data = new HashMap<>();
        for (Parameter p : inputData) {
            data.put(p.getIdentifier(), p.getValue());
        }
        return data;
    }

    @Data
    public static class Parameter {
        private String identifier;
        private Object value;
    }


}
