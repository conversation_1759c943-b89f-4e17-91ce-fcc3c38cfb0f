package com.fx.broker.tcp.protocol.processor;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.fx.broker.tcp.domain.ProtocolMessage;
import com.fx.broker.tcp.enums.FunctionCodeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 实现功能码10：读取网关当前运行状态信息
 * 运行信息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RuntimeInfoProcessor extends BaseCommandProcessor {

    @Override
    public Integer getFunctionCode() {
        return FunctionCodeEnum.RUNTIME_INFO.getCode();
    }

    @Override
    public String getProcessorName() {
        return "运行信息处理器";
    }

    @Override
    protected ProtocolMessage doProcess(ProtocolMessage message) {
        // 功能码10主要用于服务器查询运行信息，网关回复
        if (validateUplinkResponse(message)) {
            // 处理网关的运行信息回复
            handleRuntimeInfoResponse(message);
            return NO_RESPONSE; // 上行响应不需要再回复
        } else {
            log.warn("运行信息消息格式错误 - 连接ID: {}, 上行: {}, 响应: {}",
                    message.getConnectionId(), message.isUplink(), message.isResponse());
            return createErrorResponse(message, 4); // 上报格式错误
        }
    }

    /**
     * 处理网关的运行信息回复
     */
    private void handleRuntimeInfoResponse(ProtocolMessage message) {
        String gatewayId = getGatewayId(message);
        log.info("收到运行信息回复 - 网关ID: {}, 连接ID: {}", gatewayId, message.getConnectionId());

        // 验证网关是否已认证
        if (!validateGatewayAuthenticated(gatewayId)) {
            log.warn("网关未认证 - 网关ID: {}", gatewayId);
            return;
        }

        Integer retCode = message.getRetCode();
        if (retCode == null) {
            log.warn("运行信息回复缺少结果码 - 网关ID: {}", gatewayId);
            return;
        }

        if (retCode != 0) {
            log.warn("网关查询运行信息失败 - 网关ID: {}, 错误码: {}", gatewayId, retCode);
            return;
        }

        log.info("收到运行信息回复 - 网关ID: {}", gatewayId);

        // 解析运行信息
        RuntimeInfo runtimeInfo = parseRuntimeInfo(message);
        if (runtimeInfo != null) {
            // 处理运行信息
            handleRuntimeInfoData(gatewayId, runtimeInfo);
        } else {
            log.warn("解析运行信息失败 - 网关ID: {}", gatewayId);
        }
    }

    /**
     * 解析运行信息
     */
    private RuntimeInfo parseRuntimeInfo(ProtocolMessage message) {
        try {
            JSONObject data = message.getData();
            if (data == null) {
                return null;
            }

            RuntimeInfo info = new RuntimeInfo();
            info.setGatewayId(getGatewayId(message));
            info.setStartTime(data.getLong("start_time"));

            // 解析OTA信息
            JSONObject ota = data.getJSONObject("ota");
            if (ota != null) {
                OTAInfo otaInfo = new OTAInfo();
                otaInfo.setIssuedTime(ota.getLong("issued_time"));
                otaInfo.setOnlineTime(ota.getLong("online_time"));
                otaInfo.setIssuedPeople(ota.getStr("issued_people"));
                info.setOta(otaInfo);
            }

            // 解析RS485信息
            JSONObject rs485 = data.getJSONObject("re485");
            if (rs485 != null) {
                RS485Info rs485Info = new RS485Info();
                rs485Info.setConfigTime(rs485.getLong("config_time"));
                rs485Info.setOnlineTime(rs485.getLong("online_time"));
                rs485Info.setConfigPeople(rs485.getStr("config_people"));
                info.setRs485(rs485Info);
            }

            // 解析图片配置信息
            JSONObject img = data.getJSONObject("img");
            if (img != null) {
                ImageInfo imgInfo = new ImageInfo();
                imgInfo.setConfigTime(img.getLong("config_time"));
                imgInfo.setOnlineTime(img.getLong("online_time"));
                imgInfo.setConfigPeople(img.getStr("config_people"));
                info.setImg(imgInfo);
            }

            // 解析任务信息
            JSONArray taskArray = data.getJSONArray("task");
            if (taskArray != null) {
                java.util.List<TaskInfo> tasks = new java.util.ArrayList<>();
                for (int i = 0; i < taskArray.size(); i++) {
                    JSONObject taskObj = taskArray.getJSONObject(i);
                    if (taskObj != null) {
                        TaskInfo taskInfo = new TaskInfo();
                        taskInfo.setName(taskObj.getStr("name"));
                        taskInfo.setState(taskObj.getInt("state"));
                        taskInfo.setPriority(taskObj.getInt("priority"));
                        taskInfo.setStackMin(taskObj.getInt("stack_min"));
                        tasks.add(taskInfo);
                    }
                }
                info.setTasks(tasks);
            }

            info.setQueryTime(System.currentTimeMillis() / 1000);
            return info;

        } catch (Exception e) {
            log.error("解析运行信息失败 - 网关ID: {}", getGatewayId(message), e);
            return null;
        }
    }

    /**
     * 主动查询网关的运行信息
     *
     * @param gatewayId 网关ID
     * @return 是否发送成功
     */
    public boolean queryRuntimeInfo(String gatewayId) {
        try {
            log.info("查询网关运行信息 - 网关ID: {}", gatewayId);

            // 检查网关是否在线（假设connectionManager存在此方法）
            // TODO: 实现网关在线检查逻辑
            // if (!connectionManager.isGatewayOnline(gatewayId)) {
            // log.warn("网关不在线，无法查询运行信息 - 网关ID: {}", gatewayId);
            // return false;
            // }

            // 构建查询运行信息消息
            ProtocolMessage queryMessage = new ProtocolMessage();
            queryMessage.setIsUplink(false); // 下行消息
            queryMessage.setIsResponse(false); // 发送消息
            queryMessage.setPackId(0); // 使用默认包序号
            queryMessage.setCode(10);
            queryMessage.setData(new JSONObject()); // 查询运行信息不需要数据内容

            // 设置网关信息
            queryMessage.setGatewayId(gatewayId);

            // 编码消息
            String messageContent = messageCodec.encode(queryMessage);
            if (messageContent == null) {
                log.error("查询运行信息消息编码失败 - 网关ID: {}", gatewayId);
                return false;
            }

            // 发送查询请求
            // TODO: 实现发送消息到网关的逻辑
            // boolean sent = connectionManager.sendMessageToGateway(gatewayId,
            // messageContent);
            boolean sent = true; // 临时返回true
            if (sent) {
                log.info("查询运行信息请求发送成功 - 网关ID: {}", gatewayId);
                // 记录查询事件
                recordRuntimeInfoQueryEvent(gatewayId);
            } else {
                log.error("查询运行信息请求发送失败 - 网关ID: {}", gatewayId);
            }

            return sent;

        } catch (Exception e) {
            log.error("查询运行信息异常 - 网关ID: {}", gatewayId, e);
            return false;
        }
    }

    /**
     * 处理运行信息数据
     */
    private void handleRuntimeInfoData(String gatewayId, RuntimeInfo runtimeInfo) {
        try {
            log.info("处理运行信息数据 - 网关ID: {}, 启动时间: {}, 任务数: {}",
                    gatewayId, runtimeInfo.getStartTime(),
                    runtimeInfo.getTasks() != null ? runtimeInfo.getTasks().size() : 0);

            // 发送信息到MQ进行业务处理
            publishRuntimeInfoToMQ(runtimeInfo);

            // 记录运行信息事件
            recordRuntimeInfoEvent(gatewayId, runtimeInfo);

            // 分析异常任务
            analyzeTaskStatus(gatewayId, runtimeInfo.getTasks());

        } catch (Exception e) {
            log.error("处理运行信息数据失败 - 网关ID: {}", gatewayId, e);
        }
    }

    /**
     * 分析任务状态
     */
    private void analyzeTaskStatus(String gatewayId, java.util.List<TaskInfo> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return;
        }

        int runningTasks = 0;
        int suspendedTasks = 0;
        int deletedTasks = 0;
        java.util.List<String> problemTasks = new java.util.ArrayList<>();

        for (TaskInfo task : tasks) {
            switch (task.getState()) {
                case 0: // 运行
                    runningTasks++;
                    break;
                case 1: // 就绪
                    break;
                case 2: // 阻塞
                    break;
                case 3: // 挂起
                    suspendedTasks++;
                    problemTasks.add(task.getName() + "(挂起)");
                    break;
                case 4: // 删除
                    deletedTasks++;
                    problemTasks.add(task.getName() + "(删除)");
                    break;
            }

            // 检查栈空间
            if (task.getStackMin() != null && task.getStackMin() < 50) {
                problemTasks.add(task.getName() + "(栈空间不足:" + task.getStackMin() + ")");
            }
        }

        if (!problemTasks.isEmpty()) {
            log.warn("网关存在异常任务 - 网关ID: {}, 异常任务: {}", gatewayId, problemTasks);
            // TODO: 发送告警消息
        }

        log.info("任务状态统计 - 网关ID: {}, 运行: {}, 挂起: {}, 删除: {}, 总计: {}",
                gatewayId, runningTasks, suspendedTasks, deletedTasks, tasks.size());
    }

    /**
     * 发布运行信息到MQ
     */
    private void publishRuntimeInfoToMQ(RuntimeInfo runtimeInfo) {
        try {
            JSONObject mqMessage = new JSONObject();
            mqMessage.set("gateway_id", runtimeInfo.getGatewayId());
            mqMessage.set("runtime_info", runtimeInfo);
            mqMessage.set("message_type", "gateway_runtime_info");
            mqMessage.set("timestamp", System.currentTimeMillis() / 1000);

            // TODO: 集成RocketMQ发布器
            log.debug("发布运行信息到MQ - 网关ID: {}", runtimeInfo.getGatewayId());

            // 模拟MQ发布
            // rocketMQPublisher.publish("gateway_runtime_info", mqMessage.toString());

        } catch (Exception e) {
            log.error("发布运行信息到MQ失败", e);
        }
    }

    /**
     * 记录查询事件
     */
    private void recordRuntimeInfoQueryEvent(String gatewayId) {
        try {
            JSONObject queryEvent = new JSONObject();
            queryEvent.set("gateway_id", gatewayId);
            queryEvent.set("query_type", "runtime_info");
            queryEvent.set("query_time", System.currentTimeMillis() / 1000);
            queryEvent.set("event_type", "runtime_info_query");

            // TODO: 发送查询事件到MQ
            log.debug("记录运行信息查询事件 - 事件: {}", queryEvent);

        } catch (Exception e) {
            log.error("记录查询事件失败 - 网关ID: {}", gatewayId, e);
        }
    }

    /**
     * 记录运行信息事件
     */
    private void recordRuntimeInfoEvent(String gatewayId, RuntimeInfo runtimeInfo) {
        try {
            JSONObject infoEvent = new JSONObject();
            infoEvent.set("gateway_id", gatewayId);
            infoEvent.set("start_time", runtimeInfo.getStartTime());
            infoEvent.set("task_count", runtimeInfo.getTasks() != null ? runtimeInfo.getTasks().size() : 0);
            infoEvent.set("has_ota_info", runtimeInfo.getOta() != null);
            infoEvent.set("has_rs485_info", runtimeInfo.getRs485() != null);
            infoEvent.set("has_img_info", runtimeInfo.getImg() != null);
            infoEvent.set("info_time", System.currentTimeMillis() / 1000);
            infoEvent.set("event_type", "runtime_info_received");

            // TODO: 发送信息事件到MQ或记录到数据库
            log.debug("记录运行信息事件 - 事件: {}", infoEvent);

        } catch (Exception e) {
            log.error("记录运行信息事件失败 - 网关ID: {}", gatewayId, e);
        }
    }

    /**
     * 批量查询多个网关的运行信息
     *
     * @param gatewayIds 网关ID列表
     * @return 查询结果统计
     */
    public BatchRuntimeQueryResult batchQueryRuntimeInfo(java.util.List<String> gatewayIds) {
        int successCount = 0;
        int failCount = 0;
        java.util.List<String> failedGateways = new java.util.ArrayList<>();

        for (String gatewayId : gatewayIds) {
            boolean success = queryRuntimeInfo(gatewayId);
            if (success) {
                successCount++;
            } else {
                failCount++;
                failedGateways.add(gatewayId);
            }

            // 批量查询间隔
            try {
                Thread.sleep(300); // 300ms间隔，运行信息相对较大
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        BatchRuntimeQueryResult result = new BatchRuntimeQueryResult();
        result.setTotalCount(gatewayIds.size());
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setFailedGateways(failedGateways);

        log.info("批量查询运行信息完成 - 总数: {}, 成功: {}, 失败: {}",
                gatewayIds.size(), successCount, failCount);

        return result;
    }

    /**
     * 获取运行信息查询统计
     */
    public JSONObject getRuntimeInfoStatistics(String gatewayId) {
        JSONObject stats = new JSONObject();
        stats.set("gateway_id", gatewayId);
        stats.set("total_queries", 0); // TODO: 从统计服务获取
        stats.set("successful_queries", 0);
        stats.set("failed_queries", 0);
        stats.set("last_query_time", 0);
        stats.set("last_start_time", 0);
        stats.set("avg_task_count", 0);
        return stats;
    }

    // 内部数据类
    @Data
    public static class RuntimeInfo {
        private String gatewayId;
        private Long startTime;
        private OTAInfo ota;
        private RS485Info rs485;
        private ImageInfo img;
        private java.util.List<TaskInfo> tasks;
        private Long queryTime;

    }

    @Data
    public static class OTAInfo {
        private Long issuedTime;
        private Long onlineTime;
        private String issuedPeople;

    }

    @Data
    public static class RS485Info {
        private Long configTime;
        private Long onlineTime;
        private String configPeople;

    }

    @Data
    public static class ImageInfo {
        private Long configTime;
        private Long onlineTime;
        private String configPeople;

    }

    @Data
    public static class TaskInfo {
        private String name;
        private Integer state;
        private Integer priority;
        private Integer stackMin;

    }

    /**
     * 批量查询运行信息结果
     */
    @Data
    public static class BatchRuntimeQueryResult {
        private int totalCount;
        private int successCount;
        private int failCount;
        private java.util.List<String> failedGateways;
    }
}