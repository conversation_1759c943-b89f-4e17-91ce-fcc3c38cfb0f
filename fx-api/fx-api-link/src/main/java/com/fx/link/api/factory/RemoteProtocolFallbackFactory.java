package com.fx.link.api.factory;

import com.fx.common.core.domain.R;
import com.fx.common.core.web.domain.AjaxResult;
import com.fx.link.api.RemoteProtocolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @program: fxlinks
 * @description: 协议管理服务降级处理
 * @packagename: com.fx.link.api.factory
 * @author: fx
 * @e-mainl: <EMAIL>
 * @date: 2022-07-11 15:17
 **/
@Component
public class RemoteProtocolFallbackFactory implements FallbackFactory<RemoteProtocolService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteProtocolFallbackFactory.class);

    @Override
    public RemoteProtocolService create(Throwable throwable) {
        log.error("协议管理服务调用失败:{}", throwable.getMessage());
        return new RemoteProtocolService() {
            @Override
            public AjaxResult protocolScriptCacheRefresh() {
                return AjaxResult.error("协议脚本缓存刷新失败:" + throwable.getMessage());
            }

            @Override
            public R<String> getScriptByConvertKey(String convertKey) {
                log.error("获取协议转换脚本失败，convertKey: {}, 错误: {}", convertKey, throwable.getMessage());
                return R.fail("获取协议转换脚本失败:" + throwable.getMessage());
            }
        };
    }

}
